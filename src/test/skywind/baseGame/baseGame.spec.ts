import { expect, should, use } from "chai";
use(require("chai-as-promised"));
use(should);

import {
    BaseGamePlayFlow,
    BaseGameConfig,
    BaseGameContext,
    BaseGame,
    BaseGameImpl,
    BaseGameInitFlow,
    BaseGameContextAdapter,
    BaseGameActionValidator,
    BaseGameResponseAdapter,
    BaseGameInitResponse,
    BaseGameAction,
    BaseGamePlayRequest,
    WrongRequest,
    LimitsNotValid,
    SomeGameFlow,
    BaseRequest,
    GameContext
} from "../../../index";

import {
    SomeAction,
    createFlow
} from "./helpers";

describe("BaseGame", () => {
    let game: BaseGame;
    let flow: SomeGameFlow<BaseRequest>;
    let config: BaseGameConfig;

    beforeEach(() => {
        config = <BaseGameConfig>{
            gameId: "gameId",
            requiredSettings: ["something1", "something2"],
            gameActions: {
                play : new SomeAction("play", 10)
            }
        };

        game = new BaseGameImpl(config);
    });

    describe("init", () => {
        beforeEach(() => {
            flow = createFlow("init");
        });

        it("should return response created by default response adapter", async () => {
            const response = await game.init(<BaseGameInitFlow>flow);
            expect(response).to.be.deep.equal({
                request : "init",
                gameId  : "gameId",
                name    : "name",
                version : undefined,
                settings: {
                    something1 : 1,
                    something2 : 2,
                },
                previousResult: undefined
            });
        });

        it("should apply configured adapters", async() => {
            config.contextAdapter = new (class implements BaseGameContextAdapter {
                async fillInitialContext(flow: BaseGameInitFlow, context: BaseGameContext): Promise<BaseGameContext> {
                    context["something"] = "else";
                    return context;
                }
                cleanUpContext(context: BaseGameContext) {}
            });
            config.responseAdapter = new (class implements BaseGameResponseAdapter {
                createInitResponse(game: BaseGame, flow: BaseGameInitFlow, context: BaseGameContext): BaseGameInitResponse {
                    interface InitrResp extends BaseGameInitResponse {
                        something: string;
                    }

                    return <InitrResp>{
                        request       : flow.request().request,
                        gameId        : game.gameId,
                        version       : game.version,
                        name          : flow.request().name,
                        settings      : flow.settings(),
                        previousResult: context.previousResult,
                        something     : context["something"]
                    };
                }
            });

            const game = new BaseGameImpl(config);

            const response = await game.init(<BaseGameInitFlow>flow);
            expect(response).to.be.deep.equal({
                request : "init",
                gameId  : "gameId",
                name    : "name",
                version : undefined,
                settings: {
                    something1 : 1,
                    something2 : 2,
                },
                previousResult: undefined,
                something     : "else"
            });
        });

        it("should throw an error if some of the flow is missing some of required settings", (done) => {
            flow.settings = (): any => {
                return {
                    something1: 1
                };
            };
            expect(game.init(<BaseGameInitFlow>flow)).to.be.rejectedWith(LimitsNotValid).notify(done);
        });

        it("should throw an error if init is called without init request", (done) => {
            flow = createFlow("play");
            expect(game.init(<BaseGameInitFlow>flow)).to.be.rejectedWith(WrongRequest).notify(done);
        });
    });

    describe("play", () => {
        beforeEach(() => {
            flow = createFlow("play");
        });

        it("should return response returned by action", async() => {
            const response = await game.play(<BaseGamePlayFlow>flow);
            expect(response).to.be.deep.equal({
                totalWin  : 10,
                totalBet  : 11,
                roundEnded: true
            });
        });

        it("should throw an error if action isn't defined", (done) => {
            flow = createFlow("somethingelse");
            expect(game.play(<BaseGamePlayFlow>flow)).to.be.rejectedWith(WrongRequest).notify(done);
        });

        it("should throw an error if action name is one of Object.prototype property names", (done) => {
            flow = createFlow("toString");
            expect(game.play(<BaseGamePlayFlow>flow)).to.be.rejectedWith(WrongRequest).notify(done);
        });

        it("should throw an error if action validator fails play request", (done) => {
            config.actionValidator = new (class implements BaseGameActionValidator {
                validateAction(flow: BaseGamePlayFlow, context: BaseGameContext,
                    requestType: string, action: BaseGameAction): Promise<void> {
                    throw new Error("something bad");
                }
            });
            expect((new BaseGameImpl(config)).play(<BaseGamePlayFlow>flow))
                .to.be.rejectedWith(Error).notify(done);
        });

        it("should throw an error if action's validator fails", (done) => {
            config.gameActions.play.validateState = function(flow: BaseGamePlayFlow, request: BaseGamePlayRequest,
                context: BaseGameContext): Promise<void> {

                throw new Error("something bad");
            };

            expect((new BaseGameImpl(config)).play(<BaseGamePlayFlow>flow))
                .to.be.rejectedWith(Error).notify(done);
        });

        it("should update game context with cleaned result", (done) => {
            let updateDone = false;

            config.gameActions.play.validateState = function(flow: BaseGamePlayFlow, request: BaseGamePlayRequest,
                context: BaseGameContext): Promise<void> {
                throw new Error("something bad");
            };

            config.contextAdapter.cleanUpContext = (context: GameContext) => {
                context["cleaned"] = true;
            };

            flow.updateGameContext = function (context: GameContext): Promise<GameContext> {
                expect(context["cleaned"]).to.be.true;
                return new Promise((resolve, reject) => process.nextTick(() => {
                    updateDone = true;
                    resolve(context);
                }));
            };

            expect((new BaseGameImpl(config)).play(<BaseGamePlayFlow>flow))
                .to.be.rejectedWith(Error).notify((error) => {
                    expect(updateDone).to.be.true;
                    done(error);
                });
        });
    });
});